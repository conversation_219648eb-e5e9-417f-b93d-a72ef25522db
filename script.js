// Application State
let currentOrder = [];
let currentDiscount = { percentage: 0, amount: 0 };
let currentCustomer = null;
let currentTheme = 'light';
let isProcessingCheckout = false; // Add checkout state flag
let userPreferences = {
    preferredMode: 'hybrid',
    conversationUsage: 0,
    buttonUsage: 0,
    skillLevel: 'beginner',
    theme: 'light'
};
let chatHistory = [];

// Smart Insights System
let insightData = {
    orderHistory: [],
    sessionStats: {
        ordersCompleted: 0,
        totalSales: 0,
        averageOrderValue: 0,
        mostOrderedItems: {},
        timeSpentInSession: Date.now()
    },
    insights: [],
    lastInsightUpdate: 0
};

// DOM Elements
const chatInput = document.getElementById('chatInput');
const sendBtn = document.getElementById('sendBtn');
const chatContainer = document.getElementById('chatContainer');
const orderItems = document.getElementById('orderItems');
const subtotalEl = document.getElementById('subtotal');
const discountLine = document.getElementById('discountLine');
const discountLabel = document.getElementById('discountLabel');
const discountAmount = document.getElementById('discountAmount');
const customerInfo = document.getElementById('customerInfo');
const customerName = document.getElementById('customerName');
const taxEl = document.getElementById('tax');
const totalEl = document.getElementById('total');
const checkoutBtn = document.getElementById('checkoutBtn');
const modeToggle = document.getElementById('modeToggle');
// User level indicator removed from header
const productGrid = document.getElementById('productGrid');
const conversationArea = document.getElementById('conversationArea');
const themeToggle = document.getElementById('themeToggle');
const themeDropdown = document.getElementById('themeDropdown');
const currentThemeSpan = document.getElementById('currentTheme');
const toggleGrid = document.getElementById('toggleGrid');
const gridToggleText = document.getElementById('gridToggleText');
// User preference element removed with Smart Insights section
const voiceBtn = document.getElementById('voiceBtn');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadTheme();
    updateUserInterface();
    
    // Initialize category system
    initializeCategoriesSystem();
    
    // Initialize insights system
    setTimeout(() => {
        generateInsights();
        // Set up periodic insight updates
        setInterval(() => {
            if (Date.now() - insightData.lastInsightUpdate > 30000) { // Update every 30 seconds
                generateInsights();
            }
        }, 30000);
        
        // Initialize Smart Insights toggle functionality
        // The toggleInsights button event listener is already defined in setupEventListeners
        // This prevents duplicate event listeners that would hide the entire panel
    }, 2000);
});

function initializeApp() {
    // Load user preferences from localStorage
    const savedPrefs = localStorage.getItem('simplePosPrefs');
    if (savedPrefs) {
        userPreferences = { ...userPreferences, ...JSON.parse(savedPrefs) };
    }
    
    // Load insight data from localStorage
    const savedInsights = localStorage.getItem('simplePosInsights');
    if (savedInsights) {
        const parsed = JSON.parse(savedInsights);
        insightData = { ...insightData, ...parsed };
        // Reset session start time but keep historical data
        insightData.sessionStats.timeSpentInSession = Date.now();
    }

    // Set initial focus
    chatInput.focus();

    // Update UI based on preferences
    updateUserLevel();
    
    // Initially align Current Order with Quick Select Products
    alignOrderWithProducts();

    // Handle window resize to maintain proper layout
    let resizeTimeout;
    let previousWidth = window.innerWidth;

    window.addEventListener('resize', function() {
        // Clear previous timeout
        clearTimeout(resizeTimeout);

        // Debounce the resize handling
        resizeTimeout = setTimeout(() => {
            const currentWidth = window.innerWidth;
            const rightColumn = document.getElementById('orderSummaryColumn');

            // Check if we crossed the desktop breakpoint (1024px)
            const wasDesktop = previousWidth >= 1024;
            const isDesktop = currentWidth >= 1024;

            if (rightColumn) {
                if (!isDesktop) {
                    // On mobile/tablet, always reset margin
                    rightColumn.style.marginTop = '';
                } else if (wasDesktop !== isDesktop) {
                    // Only realign when crossing breakpoints, not on every resize
                    rightColumn.style.marginTop = '';
                    setTimeout(() => {
                        alignOrderWithProducts();
                    }, 50);
                }
                // If staying on desktop, don't change the alignment
            }

            previousWidth = currentWidth;
        }, 250);
    });
}

function setupEventListeners() {
    // Chat input
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    sendBtn.addEventListener('click', sendMessage);
    
    // Product cards
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('click', function() {
            const product = this.dataset.product;
            const price = parseFloat(this.dataset.price);
            addToOrder(product, price);
            userPreferences.buttonUsage++;
            updateUserLevel();
            
            // Auto-generate conversation in hybrid mode
            const currentMode = modeToggle.getAttribute('data-current-mode') || 'Hybrid Mode';
            if (currentMode === 'Hybrid Mode') {
                addAIMessage(`Added ${product} to your order! Anything else?`);
            }
            
            // Visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // Mode toggle
    modeToggle.addEventListener('click', toggleMode);
    
    // Theme toggle
    themeToggle.addEventListener('click', function() {
        themeDropdown.classList.toggle('hidden');
    });
    
    // Theme options
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            const theme = this.dataset.theme;
            changeTheme(theme);
            themeDropdown.classList.add('hidden');
        });
    });
    
    // Close theme dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!themeToggle.contains(e.target) && !themeDropdown.contains(e.target)) {
            themeDropdown.classList.add('hidden');
        }
    });
    
    // Grid toggle
    toggleGrid.addEventListener('click', toggleProductGrid);
    
    // Checkout button
    checkoutBtn.addEventListener('click', processCheckout);
    
    // Suggestions
    document.querySelectorAll('.suggestion-item').forEach(item => {
        item.addEventListener('click', function() {
            chatInput.value = this.textContent.replace(/"/g, '');
            sendMessage();
        });
    });
    
    // Discount dropdown functionality
    const discountBtn = document.getElementById('discountBtn');
    const discountDropdown = document.getElementById('discountDropdown');
    
    discountBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        discountDropdown.classList.toggle('hidden');
    });

    // Prevent discount dropdown from closing when clicking inside it
    discountDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Customer dropdown functionality
    const customerBtn = document.getElementById('customerBtn');
    const customerDropdown = document.getElementById('customerDropdown');
    const customerNameInput = document.getElementById('customerNameInput');
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    const cancelCustomerBtn = document.getElementById('cancelCustomerBtn');
    const removeCustomer = document.getElementById('removeCustomer');
    
    customerBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        customerDropdown.classList.toggle('hidden');
        if (!customerDropdown.classList.contains('hidden')) {
            setTimeout(() => customerNameInput.focus(), 100);
        }
    });

    // Prevent customer dropdown from closing when clicking inside it
    customerDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    addCustomerBtn.addEventListener('click', function() {
        const name = customerNameInput.value.trim();
        if (name) {
            currentCustomer = name;
            updateCustomerDisplay();
            customerNameInput.value = '';
            customerDropdown.classList.add('hidden');
            addAIMessage(`Added customer: ${name}`);
        }
    });
    
    cancelCustomerBtn.addEventListener('click', function() {
        customerNameInput.value = '';
        customerDropdown.classList.add('hidden');
    });
    
    // Quick customer options
    document.querySelectorAll('.customer-quick').forEach(option => {
        option.addEventListener('click', function() {
            const name = this.dataset.name;
            currentCustomer = name;
            updateCustomerDisplay();
            customerDropdown.classList.add('hidden');
            addAIMessage(`Added customer: ${name}`);
        });
    });
    
    // Remove customer
    removeCustomer.addEventListener('click', function() {
        currentCustomer = null;
        updateCustomerDisplay();
        addAIMessage("Customer removed from order.");
    });
    
    // Handle Enter key in customer input
    customerNameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addCustomerBtn.click();
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // Close discount dropdown if clicking outside
        if (!discountBtn.contains(e.target) && !discountDropdown.contains(e.target)) {
            discountDropdown.classList.add('hidden');
        }
        // Close customer dropdown if clicking outside
        if (!customerBtn.contains(e.target) && !customerDropdown.contains(e.target)) {
            customerDropdown.classList.add('hidden');
        }
    });
    
    // Handle discount option selection
    document.querySelectorAll('.discount-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.stopPropagation();
            const discount = this.dataset.discount;
            chatInput.value = `apply ${discount}% discount`;
            sendMessage();
            discountDropdown.classList.add('hidden');
        });
    });
    
    // Quick actions (excluding discount and customer which are now handled above)
    document.querySelectorAll('.quick-action').forEach(action => {
        action.addEventListener('click', function() {
            const text = this.textContent.trim();
            if (text.includes('Receipt')) {
                chatInput.value = 'print receipt';
                sendMessage();
            } else if (text.includes('Void')) {
                chatInput.value = 'remove last item';
                sendMessage();
            }
        });
    });
    



    // Voice button (mock)
    voiceBtn.addEventListener('click', function() {
        this.classList.toggle('text-red-600');
        if (this.classList.contains('text-red-600')) {
            addAIMessage("🎤 Voice listening active. Speak your order now...");
            setTimeout(() => {
                this.classList.remove('text-red-600');
                addAIMessage("Voice input: '2 lattes and a croissant'");
                processNaturalLanguage('2 lattes and a croissant');
            }, 3000);
        }
    });
    
    // Toggle insights panel
    document.getElementById('toggleInsights').addEventListener('click', function() {
        const container = document.getElementById('insightsContainer');
        const toggleText = document.getElementById('insightsToggleText');
        const icon = this.querySelector('i');
        
        if (container.style.display === 'none') {
            container.style.display = 'block';
            toggleText.textContent = 'Hide';
            icon.className = 'fas fa-eye mr-1';
        } else {
            container.style.display = 'none';
            toggleText.textContent = 'Show';
            icon.className = 'fas fa-eye-slash mr-1';
        }
    });
    
    // Clear chat
    document.getElementById('clearChat').addEventListener('click', function() {
        chatHistory = [];
        chatContainer.innerHTML = `
            <div class="message-bubble mb-4">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: var(--accent-primary);">
                        <i class="fas fa-robot text-sm" style="color: var(--bg-primary);"></i>
                    </div>
                    <div class="rounded-2xl rounded-tl-md px-4 py-2 max-w-xs" style="background-color: var(--bg-accent);">
                        <p class="text-sm" style="color: var(--text-primary);">Chat cleared! How can I help you with your next order?</p>
                    </div>
                </div>
            </div>
        `;
    });

    // Setup new UI elements
    setupCompactQuickActions();
    setupHorizontalActionsBar();
    setupMobileTabs();
}

// Setup functions for new UI elements
function setupCompactQuickActions() {
    // Essential quick action buttons only
    const printReceiptBtn = document.getElementById('printReceiptBtn');
    const voidItemBtn = document.getElementById('voidItemBtn');

    if (printReceiptBtn) {
        printReceiptBtn.addEventListener('click', function() {
            chatInput.value = 'print receipt';
            sendMessage();
        });
    }

    if (voidItemBtn) {
        voidItemBtn.addEventListener('click', function() {
            chatInput.value = 'remove last item';
            sendMessage();
        });
    }
}


function setupHorizontalActionsBar() {
    const horizontalActionsBar = document.getElementById('horizontalActionsBar');
    const hDiscountBtn = document.getElementById('hDiscountBtn');
    const hCustomerBtn = document.getElementById('hCustomerBtn');
    const hReceiptBtn = document.getElementById('hReceiptBtn');
    const hVoidBtn = document.getElementById('hVoidBtn');
    const hCheckoutBtn = document.getElementById('hCheckoutBtn');
    const hClearBtn = document.getElementById('hClearBtn');

    // Horizontal action handlers
    if (hDiscountBtn) {
        hDiscountBtn.addEventListener('click', function() {
            document.getElementById('discountBtn').click();
        });
    }

    if (hCustomerBtn) {
        hCustomerBtn.addEventListener('click', function() {
            document.getElementById('customerBtn').click();
        });
    }

    if (hReceiptBtn) {
        hReceiptBtn.addEventListener('click', function() {
            chatInput.value = 'print receipt';
            sendMessage();
        });
    }

    if (hVoidBtn) {
        hVoidBtn.addEventListener('click', function() {
            chatInput.value = 'remove last item';
            sendMessage();
        });
    }

    if (hCheckoutBtn) {
        hCheckoutBtn.addEventListener('click', function() {
            document.getElementById('checkoutBtn').click();
        });
    }

    if (hClearBtn) {
        hClearBtn.addEventListener('click', function() {
            currentOrder = [];
            updateOrderDisplay();
            addAIMessage("Order cleared! Ready for a new order.");
        });
    }

    // Optional: Toggle horizontal bar visibility (can be controlled by user preference)
    // You can add a toggle button in the header if needed
}

function sendMessage() {
    const message = chatInput.value.trim();
    if (!message) return;
    
    // Add user message
    addUserMessage(message);
    chatInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    // Process message
    setTimeout(() => {
        processNaturalLanguage(message);
        hideTypingIndicator();
        userPreferences.conversationUsage++;
        updateUserLevel();
        savePreferences();
    }, 1000);
}

function addUserMessage(message) {
    const messageEl = document.createElement('div');
    messageEl.className = 'message-bubble mb-4';
    messageEl.innerHTML = `
        <div class="flex items-start space-x-2 sm:space-x-3 justify-end">
            <div class="rounded-2xl rounded-tr-md px-3 sm:px-4 py-2 sm:py-3 max-w-xs sm:max-w-sm" style="background-color: var(--accent-primary);">
                <p class="text-sm sm:text-base" style="color: var(--bg-primary);">${message}</p>
            </div>
            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--accent-primary);">
                <i class="fas fa-user text-sm" style="color: var(--bg-primary);"></i>
            </div>
        </div>
    `;
    chatContainer.appendChild(messageEl);
    chatContainer.scrollTop = chatContainer.scrollHeight;
    chatHistory.push({ type: 'user', message });
}

function addAIMessage(message) {
    const messageEl = document.createElement('div');
    messageEl.className = 'message-bubble mb-4';
    messageEl.innerHTML = `
        <div class="flex items-start space-x-2 sm:space-x-3">
            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--accent-primary);">
                <i class="fas fa-robot text-sm" style="color: var(--bg-primary);"></i>
            </div>
            <div class="rounded-2xl rounded-tl-md px-3 sm:px-4 py-2 sm:py-3 max-w-xs sm:max-w-sm" style="background-color: var(--bg-accent);">
                <p class="text-sm sm:text-base" style="color: var(--text-primary);">${message}</p>
            </div>
        </div>
    `;
    chatContainer.appendChild(messageEl);
    chatContainer.scrollTop = chatContainer.scrollHeight;
    chatHistory.push({ type: 'ai', message });
}

function showTypingIndicator() {
    document.getElementById('typingIndicator').classList.remove('hidden');
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').classList.add('hidden');
}

function processNaturalLanguage(input) {
    const lowerInput = input.toLowerCase();
    
    // Extract numbers and products
    const products = [
        { name: 'coffee', price: 4.50, category: 'hot-beverages', popular: true, variations: ['coffee', 'americano', 'black coffee', 'decaf', 'decaf coffee'] },
        { name: 'latte', price: 5.25, category: 'hot-beverages', popular: true, variations: ['latte', 'cafe latte', 'medium latte', 'large latte', 'small latte', 'lattes'] },
        { name: 'croissant', price: 3.75, category: 'pastries', popular: true, variations: ['croissant', 'croissants'] },
        { name: 'muffin', price: 4.00, category: 'pastries', popular: false, variations: ['muffin', 'muffins'] },
        { name: 'sandwich', price: 7.50, category: 'meals', popular: true, variations: ['sandwich', 'sandwiches'] },
        { name: 'fresh juice', price: 5.00, category: 'cold-beverages', popular: true, variations: ['juice', 'fresh juice', 'orange juice', 'apple juice', 'fruit juice'] },
        { name: 'tea', price: 3.50, category: 'hot-beverages', popular: false, variations: ['tea', 'green tea', 'black tea', 'herbal tea', 'hot tea', 'iced tea'] },
        { name: 'hot chocolate', price: 4.75, category: 'hot-beverages', popular: false, variations: ['hot chocolate', 'cocoa', 'hot cocoa', 'chocolate drink'] },
        { name: 'iced coffee', price: 5.00, category: 'cold-beverages', popular: true, variations: ['iced coffee', 'cold coffee', 'iced americano', 'cold brew'] },
        { name: 'donut', price: 2.75, category: 'pastries', popular: true, variations: ['donut', 'donuts', 'doughnut', 'doughnuts'] },
        { name: 'bagel', price: 3.25, category: 'pastries', popular: false, variations: ['bagel', 'bagels'] },
        { name: 'salad', price: 8.50, category: 'meals', popular: false, variations: ['salad', 'green salad', 'caesar salad', 'garden salad'] },
        { name: 'soup', price: 6.25, category: 'meals', popular: false, variations: ['soup', 'bowl of soup', 'hot soup'] },
        { name: 'smoothie', price: 6.75, category: 'cold-beverages', popular: true, variations: ['smoothie', 'fruit smoothie', 'protein smoothie', 'berry smoothie'] },
        { name: 'water', price: 2.00, category: 'cold-beverages', popular: false, variations: ['water', 'bottle of water', 'bottled water', 'still water', 'sparkling water'] },
        { name: 'wrap', price: 7.25, category: 'meals', popular: false, variations: ['wrap', 'wraps', 'chicken wrap', 'veggie wrap'] },
        { name: 'pastry', price: 4.25, category: 'pastries', popular: false, variations: ['pastry', 'pastries', 'danish', 'sweet pastry'] }
    ];
    
    // Handle checkout
    if (lowerInput.includes('checkout') || lowerInput.includes('pay') || lowerInput.includes('complete')) {
        if (isProcessingCheckout) {
            addAIMessage("Checkout is already in progress. Please wait...");
            return;
        }
        if (currentOrder.length > 0) {
            addAIMessage(`Processing checkout for ${currentOrder.length} items totaling ${totalEl.textContent}. Payment method?`);
            setTimeout(() => {
                processCheckout();
            }, 2000);
        } else {
            addAIMessage("Your cart is empty. Add some items first!");
        }
        return;
    }
    
    // Handle discount - but don't return, continue processing products too
    let discountProcessed = false;
    if (lowerInput.includes('discount')) {
        const discountMatch = lowerInput.match(/(\d+)%/);
        if (discountMatch) {
            const discount = parseInt(discountMatch[1]);
            currentDiscount.percentage = discount;
            addAIMessage(`Applied ${discount}% discount to your order!`);
        } else {
            currentDiscount.percentage = 10;
            addAIMessage("Applied 10% discount to your order!");
        }
        updateOrderDisplay();
        return; // Exit early to prevent duplicate message
        discountProcessed = true;
    }
    
    // Handle print receipt - but don't return, continue processing products too
    let printProcessed = false;
    if (lowerInput.includes('print') && lowerInput.includes('receipt')) {
        if (currentOrder.length > 0) {
            generateReceipt();
            printProcessed = true;
        } else {
            addAIMessage("No items to print. Add some items to your order first!");
            return; // Only return if there's nothing to print and no other commands
        }
    }
    
    // Handle item removal - process specific item removal
    let itemRemoved = false;
    if (lowerInput.includes('remove') || lowerInput.includes('delete')) {
        // Handle clear all commands - only if it's truly clearing everything
        if ((lowerInput.includes('all') && (lowerInput.includes('item') || lowerInput.includes('order'))) || 
            lowerInput.includes('everything') || 
            (lowerInput.includes('clear') && !products.some(p => p.variations.some(v => lowerInput.includes(v))))) {
            currentOrder = [];
            currentDiscount = { percentage: 0, amount: 0 };
            currentCustomer = null;
            addAIMessage("Cleared your entire order.");
            updateOrderDisplay();
            updateCustomerDisplay();
            return;
        }
        
        // Handle remove last/recent
        if (lowerInput.includes('last') || lowerInput.includes('recent')) {
            if (currentOrder.length > 0) {
                const removed = currentOrder.pop();
                addAIMessage(`Removed ${removed.name} from your order.`);
                updateOrderDisplay();
            } else {
                addAIMessage("No items to remove.");
            }
            return;
        }
        
        // Handle remove specific items
        let removedItems = [];
        let removalQuantity = 1;
        let removeAll = false;
        
        // Check for "all [item]" pattern
        if (lowerInput.includes('all ') && products.some(p => p.variations.some(v => lowerInput.includes(v)))) {
            removeAll = true;
        }
        
        // Check for quantity in removal command (only if not "all")
        if (!removeAll) {
            const removalQuantityMatch = lowerInput.match(/remove\s+(\d+)\s+|delete\s+(\d+)\s+/);
            if (removalQuantityMatch) {
                removalQuantity = parseInt(removalQuantityMatch[1] || removalQuantityMatch[2]);
            }
        }
        
        // Find items to remove by matching product variations
        products.forEach(product => {
            const hasMatch = product.variations.some(variation => lowerInput.includes(variation));
            if (hasMatch) {
                // Find items with this product name in the order
                let removed = 0;
                const targetQuantity = removeAll ? Number.MAX_SAFE_INTEGER : removalQuantity;
                
                for (let i = currentOrder.length - 1; i >= 0 && removed < targetQuantity; i--) {
                    if (currentOrder[i].name.toLowerCase() === product.name.toLowerCase()) {
                        const removedItem = currentOrder.splice(i, 1)[0];
                        removedItems.push(removedItem.name);
                        removed++;
                    }
                }
            }
        });
        
        if (removedItems.length > 0) {
            const itemsText = removedItems.length === 1 ? removedItems[0] : 
                `${removedItems.length} items (${removedItems.join(', ')})`;
            addAIMessage(`Removed ${itemsText} from your order.`);
            updateOrderDisplay();
            itemRemoved = true;
            
            // If this was purely a removal command, don't continue to product parsing
            // Check if the input is primarily about removal (no add/want/get verbs)
            const addWords = ['add', 'want', 'get', 'give', 'order', 'buy', 'purchase', 'take'];
            const hasAddWords = addWords.some(word => lowerInput.includes(word));
            const startsWithRemove = lowerInput.trim().startsWith('remove') || lowerInput.trim().startsWith('delete');
            
            if (startsWithRemove && !hasAddWords) {
                return; // Pure removal command - don't continue to product parsing
            }
        } else {
            // No matching items found to remove
            addAIMessage("I couldn't find those items in your order to remove.");
            return;
        }
    }
    
    // Extract quantities and products using improved parsing
    let foundItems = [];
    let totalAdded = 0;
    
    // First, handle discount commands separately
    let processedInput = lowerInput;
    
    // Split input into segments by common delimiters - improved regex
    const segments = processedInput.split(/\s*(?:,\s*|\s+and\s+|&\s*)/);
    
    segments.forEach(segment => {
        segment = segment.trim();
        if (!segment || segment.includes('discount')) {
            return; // Skip empty or discount segments
        }
        
        // Look for quantity patterns in each segment
        let quantity = 1;
        let productText = segment;
        
        // Check for number + product patterns
        const numberMatch = segment.match(/^(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+)/);
        if (numberMatch) {
            const quantityStr = numberMatch[1];
            productText = numberMatch[2].trim();
            
            // Convert word numbers to digits
            const wordNumbers = {
                'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
                'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
            };
            
            quantity = wordNumbers[quantityStr] || parseInt(quantityStr) || 1;
        } else {
        }
        
        // Find matching product for this segment
        let matchedProduct = null;
        let bestMatchLength = 0;
        
        products.forEach(product => {
            product.variations.forEach(variation => {
                // Check if the variation matches and is a good match
                if (productText.includes(variation) && variation.length > bestMatchLength) {
                    matchedProduct = product;
                    bestMatchLength = variation.length;
                }
            });
        });
        
        if (matchedProduct) {
            // Check if we already have this product in foundItems
            const existingItem = foundItems.find(item => item.product.name === matchedProduct.name);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                foundItems.push({ product: matchedProduct, quantity });
            }
            totalAdded += quantity;
        } else {
        }
    });
    
    // Fallback: if no segments were parsed, try the old method for simple inputs
    if (foundItems.length === 0) {
        const matchedProducts = new Set();
        
        products.forEach(product => {
            const hasMatch = product.variations.some(variation => lowerInput.includes(variation));
            
            if (hasMatch && !matchedProducts.has(product.name)) {
                foundItems.push({ product, quantity: 1 });
                matchedProducts.add(product.name);
                totalAdded += 1;
            }
        });
    }
    
    
    // Add items to order
    if (foundItems.length > 0) {
        foundItems.forEach(item => {
            for (let i = 0; i < item.quantity; i++) {
                addToOrder(item.product.name, item.product.price);
            }
        });
        
        const itemsText = foundItems.map(item => 
            `${item.quantity} ${item.product.name}${item.quantity > 1 ? 's' : ''}`
        ).join(', ');
        
        let responseMessage = `Added ${itemsText} to your order!`;
        if (discountProcessed) {
            responseMessage += ` Discount applied.`;
        }
        if (printProcessed) {
            responseMessage += ` Receipt printed.`;
        }
        responseMessage += ` Total: ${totalEl.textContent}. Anything else?`;
        addAIMessage(responseMessage);
        updateOrderDisplay();
    } else if (discountProcessed || printProcessed || itemRemoved) {
        // Only discount/print/removal was processed, no items added
        let message = "";
        if (discountProcessed) message += "Discount applied! ";
        if (printProcessed) message += "Receipt printed! ";
        if (itemRemoved && !discountProcessed && !printProcessed) message += "Items removed from order. ";
        if (discountProcessed && !printProcessed && !itemRemoved) message += "Add some items to see the discounted total.";
        addAIMessage(message.trim() || "Action completed!");
        updateOrderDisplay();
    } else {
        // Provide helpful response
        const suggestions = [
            "I didn't catch that. Try saying something like '2 coffees and a muffin'",
            "You can say things like 'add a latte' or 'I want 3 croissants'",
            "Not sure what you meant. Try 'checkout' to complete your order, or add items like 'sandwich and juice'"
        ];
        addAIMessage(suggestions[Math.floor(Math.random() * suggestions.length)]);
    }
}

// Helper function to get product icon and class
function getProductIconInfo(productName) {
    const name = productName.toLowerCase();
    const iconMap = {
        'coffee': { icon: 'fas fa-coffee', class: 'coffee' },
        'latte': { icon: 'fas fa-coffee', class: 'latte' },
        'cappuccino': { icon: 'fas fa-coffee', class: 'latte' },
        'espresso': { icon: 'fas fa-coffee', class: 'coffee' },
        'croissant': { icon: 'fas fa-bread-slice', class: 'croissant' },
        'muffin': { icon: 'fas fa-cookie-bite', class: 'muffin' },
        'sandwich': { icon: 'fas fa-hamburger', class: 'sandwich' },
        'fresh juice': { icon: 'fas fa-glass-whiskey', class: 'juice' },
        'juice': { icon: 'fas fa-glass-whiskey', class: 'juice' },
        'soup': { icon: 'fas fa-bowl-food', class: 'soup' },
        'wrap': { icon: 'fas fa-hotdog', class: 'wrap' }
    };
    
    // Try exact match first
    if (iconMap[name]) {
        return iconMap[name];
    }
    
    // Try partial match
    for (const [key, value] of Object.entries(iconMap)) {
        if (name.includes(key) || key.includes(name)) {
            return value;
        }
    }
    
    // Default fallback
    return { icon: 'fas fa-utensils', class: 'default' };
}

function addToOrder(name, price) {
    currentOrder.push({ name, price, id: Date.now() + Math.random() });
    updateOrderDisplay();
    
    // Update insights after order changes
    setTimeout(() => generateInsights(), 500);
}

// Adaptive height management functions
function getOrderDisplayMode(itemCount) {
    if (itemCount <= 5) return 'natural';
    if (itemCount <= 12) return 'moderate';
    return 'compact';
}

function updateOrderContainerHeight(mode) {
    const container = document.querySelector('.order-items-list');
    const heights = {
        'natural': 'auto',
        'moderate': '350px', 
        'compact': '300px'
    };
    if (container) {
        container.style.maxHeight = heights[mode];
    }
}

function updateItemCountBadge(itemCount) {
    const itemCountBadge = document.getElementById('itemCountBadge');
    if (itemCount > 0) {
        itemCountBadge.classList.remove('hidden');
        itemCountBadge.textContent = ` (${itemCount} item${itemCount !== 1 ? 's' : ''})`;
    } else {
        itemCountBadge.classList.add('hidden');
    }
}

function updateScrollIndicators() {
    const container = document.querySelector('.order-items-list');
    if (container) {
        const hasScroll = container.scrollHeight > container.clientHeight;
        if (hasScroll) {
            container.classList.add('has-scroll');
        } else {
            container.classList.remove('has-scroll');
        }
    }
}


function updateOrderDisplay() {
    if (currentOrder.length === 0) {
        orderItems.innerHTML = `
            <div class="empty-order-state">
                <i class="fas fa-shopping-bag empty-order-icon"></i>
                <div class="empty-order-text">No items yet</div>
                <div class="empty-order-subtext">Start by typing or clicking products</div>
            </div>
        `;
        checkoutBtn.disabled = true;
        checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium cursor-not-allowed transition-all duration-200";
        checkoutBtn.style.backgroundColor = 'var(--border-color)';
        checkoutBtn.style.color = 'var(--text-muted)';
        checkoutBtn.style.opacity = '1';
        checkoutBtn.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Complete Order';
    } else {
        // Group identical items by name and price, maintaining order
        const groupedItems = {};
        const orderTracker = []; // Track the order items first appear
        
        currentOrder.forEach((item, index) => {
            const key = `${item.name}-${item.price}`;
            if (groupedItems[key]) {
                groupedItems[key].quantity += 1;
                groupedItems[key].ids.push(item.id);
            } else {
                groupedItems[key] = {
                    name: item.name,
                    price: item.price,
                    quantity: 1,
                    ids: [item.id],
                    firstIndex: index // Track when this item type first appeared
                };
                orderTracker.push(key);
            }
        });

        // Display grouped items in the order they first appeared
        orderItems.innerHTML = orderTracker.map(key => {
            const group = groupedItems[key];
            const totalPrice = group.price * group.quantity;
            const iconInfo = getProductIconInfo(group.name);
            
            return `
                <div class="order-item-card adding">
                    <div class="order-item-header">
                        <div class="product-icon ${iconInfo.class}">
                            <i class="${iconInfo.icon}"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">${group.name}</div>
                            <div class="product-unit-price">$${group.price.toFixed(2)} each</div>
                        </div>
                        <div class="item-total-price">$${totalPrice.toFixed(2)}</div>
                        <button onclick="removeItemGroup('${group.name}', ${group.price})" class="remove-item-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="quantity-controls">
                        <button onclick="adjustQuantity('${group.name}', ${group.price}, -1)" class="quantity-btn">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity-display">${group.quantity}</span>
                        <button onclick="adjustQuantity('${group.name}', ${group.price}, 1)" class="quantity-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        // Only enable checkout if not currently processing
        if (!isProcessingCheckout) {
            checkoutBtn.disabled = false;
            checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium transition-all duration-200";
            checkoutBtn.style.backgroundColor = 'var(--success-color)';
            checkoutBtn.style.color = 'white';
            checkoutBtn.style.opacity = '1';
            checkoutBtn.style.boxShadow = ''; // Clear any processing shadow
            checkoutBtn.style.cursor = 'pointer';
            checkoutBtn.onmouseover = function() {
                if (!isProcessingCheckout) {
                    this.style.backgroundColor = '#059669'; // Darker green on hover
                    this.style.transform = 'translateY(-1px)';
                    this.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)';
                }
            };
            checkoutBtn.onmouseout = function() {
                if (!isProcessingCheckout) {
                    this.style.backgroundColor = 'var(--success-color)';
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                }
            };
            checkoutBtn.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Complete Order';
        }
    }
    
    // Update totals
    const subtotal = currentOrder.reduce((sum, item) => sum + item.price, 0);
    
    // Calculate discount
    let discountedSubtotal = subtotal;
    if (currentDiscount.percentage > 0) {
        currentDiscount.amount = subtotal * (currentDiscount.percentage / 100);
        discountedSubtotal = subtotal - currentDiscount.amount;
        
        // Show discount line
        discountLine.classList.remove('hidden');
        discountLabel.textContent = `Discount (${currentDiscount.percentage}%):`;
        discountAmount.textContent = `-$${currentDiscount.amount.toFixed(2)}`;
    } else {
        // Hide discount line
        discountLine.classList.add('hidden');
        currentDiscount.amount = 0;
    }
    
    const tax = discountedSubtotal * 0.085;
    const total = discountedSubtotal + tax;
    
    subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
    taxEl.textContent = `$${tax.toFixed(2)}`;
    totalEl.textContent = `$${total.toFixed(2)}`;

    // Update compact summary
    updateCompactSummary(subtotal, tax, total);
    
    // Apply adaptive height based on item count
    const displayMode = getOrderDisplayMode(currentOrder.length);
    updateOrderContainerHeight(displayMode);
    
    // Update item count badge in total line
    updateItemCountBadge(currentOrder.length);
    
    // Update scroll indicators
    updateScrollIndicators();
    
    // Update mobile tab badges
    updateTabBadges();
}

function updateCompactSummary(subtotal, tax, total) {
    const subtotalCompact = document.getElementById('subtotalCompact');
    const taxCompact = document.getElementById('taxCompact');
    const totalCompact = document.getElementById('totalCompact');
    const discountLineCompact = document.getElementById('discountLineCompact');
    const discountLabelCompact = document.getElementById('discountLabelCompact');
    const discountAmountCompact = document.getElementById('discountAmountCompact');

    if (subtotalCompact) subtotalCompact.textContent = `$${subtotal.toFixed(2)}`;
    if (taxCompact) taxCompact.textContent = `$${tax.toFixed(2)}`;
    if (totalCompact) totalCompact.textContent = `$${total.toFixed(2)}`;

    // Handle discount display in compact summary
    if (currentDiscount.percentage > 0 && discountLineCompact) {
        discountLineCompact.classList.remove('hidden');
        if (discountLabelCompact) discountLabelCompact.textContent = `Discount (${currentDiscount.percentage}%):`;
        if (discountAmountCompact) discountAmountCompact.textContent = `-$${currentDiscount.amount.toFixed(2)}`;
    } else if (discountLineCompact) {
        discountLineCompact.classList.add('hidden');
    }
}

function updateCustomerDisplay() {
    if (currentCustomer) {
        customerInfo.classList.remove('hidden');
        customerName.textContent = currentCustomer;
    } else {
        customerInfo.classList.add('hidden');
    }
}

function generateReceipt() {
    // Calculate totals
    const subtotal = currentOrder.reduce((sum, item) => sum + item.price, 0);
    let discountedSubtotal = subtotal;
    
    if (currentDiscount.percentage > 0) {
        discountedSubtotal = subtotal - currentDiscount.amount;
    }
    
    const tax = discountedSubtotal * 0.085;
    const total = discountedSubtotal + tax;
    
    // Generate receipt content
    const now = new Date();
    const dateStr = now.toLocaleDateString();
    const timeStr = now.toLocaleTimeString();
    
    let receiptContent = `
================================
       SIMPLEPOS RECEIPT
================================
Date: ${dateStr}
Time: ${timeStr}
${currentCustomer ? `Customer: ${currentCustomer}` : ''}

--------------------------------
ITEMS:
`;

    currentOrder.forEach(item => {
        receiptContent += `${item.name.padEnd(20)} $${item.price.toFixed(2)}\n`;
    });

    receiptContent += `
--------------------------------
Subtotal:           $${subtotal.toFixed(2)}`;

    if (currentDiscount.percentage > 0) {
        receiptContent += `
Discount (${currentDiscount.percentage}%):      -$${currentDiscount.amount.toFixed(2)}`;
    }

    receiptContent += `
Tax (8.5%):         $${tax.toFixed(2)}
TOTAL:              $${total.toFixed(2)}

================================
     Thank you for your visit!
================================`;

    // Create and show receipt modal/popup
    showReceiptModal(receiptContent);
    
    addAIMessage("Receipt generated! 🧾 Check the receipt window or print dialog.");
}

function showReceiptModal(content) {
    // Create modal backdrop
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Receipt</h3>
                <button id="closeReceipt" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <pre class="text-sm font-mono text-gray-800 whitespace-pre-wrap">${content}</pre>
            </div>
            <div class="flex space-x-3">
                <button id="printReceipt" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium">
                    <i class="fas fa-print mr-2"></i>Print
                </button>
                <button id="closeReceiptBtn" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 font-medium">
                    Close
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Handle close buttons
    const closeButtons = modal.querySelectorAll('#closeReceipt, #closeReceiptBtn');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    });
    
    // Handle print button
    const printBtn = modal.querySelector('#printReceipt');
    printBtn.addEventListener('click', () => {
        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Receipt</title>
                    <style>
                        body { font-family: 'Courier New', monospace; margin: 20px; }
                        pre { white-space: pre-wrap; }
                    </style>
                </head>
                <body>
                    <pre>${content}</pre>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
        
        // Close the modal
        document.body.removeChild(modal);
        addAIMessage("Receipt sent to printer! 🖨️");
    });
    
    // Close modal when clicking backdrop
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function removeItem(id) {
    currentOrder = currentOrder.filter(item => item.id !== parseFloat(id));
    updateOrderDisplay();
    addAIMessage("Item removed from your order.");
}

function removeItemGroup(name, price) {
    const initialCount = currentOrder.length;
    currentOrder = currentOrder.filter(item => !(item.name === name && item.price === price));
    const removedCount = initialCount - currentOrder.length;
    
    updateOrderDisplay();
    addAIMessage(`Removed all ${name} items (${removedCount} items) from your order.`);
}

function adjustQuantity(name, price, change) {
    if (change > 0) {
        // Add one more item
        addToOrder(name, price);
        addAIMessage(`Added 1 more ${name} to your order.`);
        
        // Add pulse animation to quantity display
        setTimeout(() => {
            const quantityElements = document.querySelectorAll('.quantity-display');
            quantityElements.forEach(el => {
                if (el.textContent.trim() !== '0') {
                    el.classList.add('quantity-pulse');
                    setTimeout(() => el.classList.remove('quantity-pulse'), 400);
                }
            });
        }, 100);
    } else if (change < 0) {
        // Check how many of this item type we currently have
        const itemsOfType = currentOrder.filter(item => item.name === name && item.price === price);
        const currentQuantity = itemsOfType.length;
        
        // Remove one item of this type
        const itemIndex = currentOrder.findIndex(item => item.name === name && item.price === price);
        if (itemIndex !== -1) {
            currentOrder.splice(itemIndex, 1);
            updateOrderDisplay();
            
            // Add pulse animation to quantity display
            setTimeout(() => {
                const quantityElements = document.querySelectorAll('.quantity-display');
                quantityElements.forEach(el => {
                    if (el.textContent.trim() !== '0') {
                        el.classList.add('quantity-pulse');
                        setTimeout(() => el.classList.remove('quantity-pulse'), 400);
                    }
                });
            }, 100);
            
            if (currentQuantity === 1) {
                addAIMessage(`Removed ${name} from your order.`);
            } else {
                addAIMessage(`Removed 1 ${name} from your order. ${currentQuantity - 1} remaining.`);
            }
        }
    }
}

function processCheckout() {
    if (currentOrder.length === 0) {
        addAIMessage("Your cart is empty! Add some items first.");
        return;
    }

    if (isProcessingCheckout) {
        addAIMessage("Checkout is already in progress. Please wait...");
        return;
    }

    // Set checkout state and disable button
    isProcessingCheckout = true;
    checkoutBtn.disabled = true;
    checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium cursor-not-allowed transition-all duration-200";
    // Remove any existing hover handlers
    checkoutBtn.onmouseover = null;
    checkoutBtn.onmouseout = null;
    // Set modern processing colors - orange/amber for active processing
    checkoutBtn.style.backgroundColor = '#f59e0b'; // Modern amber/orange for processing
    checkoutBtn.style.color = 'white';
    checkoutBtn.style.opacity = '1';
    checkoutBtn.style.boxShadow = '0 4px 12px rgba(245, 158, 11, 0.3)';
    checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing Payment...';

    addAIMessage(`Processing payment of ${totalEl.textContent}... Payment approved! 🎉 Receipt sent. Thank you!`);

    // Track order completion for insights
    // trackOrderCompletion([...currentOrder]); // Temporarily disabled to fix checkout flow

    // Reset order
    setTimeout(() => {
        currentOrder = [];
        currentDiscount = { percentage: 0, amount: 0 };
        currentCustomer = null;
        isProcessingCheckout = false; // Reset checkout state

        // Reset checkout button to disabled state (empty cart)
        checkoutBtn.disabled = true;
        checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium cursor-not-allowed transition-all duration-200";
        checkoutBtn.style.backgroundColor = 'var(--border-color)';
        checkoutBtn.style.color = 'var(--text-muted)';
        checkoutBtn.style.opacity = '1';
        checkoutBtn.style.boxShadow = '';
        checkoutBtn.style.transform = '';
        checkoutBtn.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Complete Order';
        checkoutBtn.onmouseover = null;
        checkoutBtn.onmouseout = null;

        updateOrderDisplay();
        updateCustomerDisplay();
        addAIMessage("Ready for your next order! 😊");
    }, 3000);
}

function toggleMode() {
    const modes = ['Hybrid Mode', 'Conversation Mode', 'Traditional Mode'];
    const currentMode = modeToggle.getAttribute('data-current-mode') || 'Hybrid Mode';
    const currentIndex = modes.indexOf(currentMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    const nextMode = modes[nextIndex];

    // Update the data attribute
    modeToggle.setAttribute('data-current-mode', nextMode);

    let icon = 'fas fa-brain';
    let shortName = 'Hybrid';
    if (nextMode === 'Conversation Mode') {
        icon = 'fas fa-comments';
        shortName = 'Chat';
    } else if (nextMode === 'Traditional Mode') {
        icon = 'fas fa-th-large';
        shortName = 'Buttons';
    }

    // Update button content with responsive text
    modeToggle.innerHTML = `<i class="${icon} mr-1 sm:mr-2"></i><span class="hidden sm:inline">${nextMode}</span><span class="sm:hidden">${shortName}</span>`;

    // Update mode indicator badge
    const modeIndicator = document.getElementById('modeIndicator');
    if (modeIndicator) {
        modeIndicator.textContent = `${shortName} Mode`;
    }

    // Get the parent container that has the space-y-6 class
    const leftColumn = productGrid.parentElement;

    // Adjust interface based on mode
    if (nextMode === 'Conversation Mode') {
        productGrid.style.display = 'none';
        conversationArea.style.display = 'block';
        // Remove spacing when only conversation area is visible
        leftColumn.classList.remove('space-y-6');
        addAIMessage("Switched to conversation mode. Just type what you want!");
    } else if (nextMode === 'Traditional Mode') {
        productGrid.style.display = 'block';
        conversationArea.style.display = 'none';
        // Ensure product container is visible when switching to traditional mode
        const productContainer = document.getElementById('productContainer');
        productContainer.style.display = 'grid';
        gridToggleText.textContent = 'Hide Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye mr-1';
        // Remove spacing when only product grid is visible
        leftColumn.classList.remove('space-y-6');
        // Don't add AI message when conversation area is hidden
    } else {
        productGrid.style.display = 'block';
        conversationArea.style.display = 'block';
        // Ensure product container is visible when switching to hybrid mode
        const productContainer = document.getElementById('productContainer');
        productContainer.style.display = 'grid';
        gridToggleText.textContent = 'Hide Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye mr-1';
        // Restore spacing when both elements are visible
        leftColumn.classList.add('space-y-6');
        addAIMessage("Switched to hybrid mode. Use conversation or click products - whatever feels natural!");
    }
    
    userPreferences.preferredMode = nextMode.toLowerCase().replace(' ', '_');
    savePreferences();
}

function toggleProductGrid() {
    const container = document.getElementById('productContainer');
    if (container.style.display === 'none') {
        container.style.display = 'grid';
        gridToggleText.textContent = 'Hide Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye mr-1';
    } else {
        container.style.display = 'none';
        gridToggleText.textContent = 'Show Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye-slash mr-1';
    }
}

function updateUserLevel() {
    const total = userPreferences.conversationUsage + userPreferences.buttonUsage;
    const conversationRatio = total > 0 ? userPreferences.conversationUsage / total : 0;

    if (total < 5) {
        userPreferences.skillLevel = 'learning';
    } else if (conversationRatio > 0.7) {
        userPreferences.skillLevel = 'expert';
    } else if (conversationRatio > 0.3) {
        userPreferences.skillLevel = 'intermediate';
    } else {
        userPreferences.skillLevel = 'traditional';
    }

            }


function updateUserInterface() {
    // Adapt interface based on user skill level
    const skillLevel = userPreferences.skillLevel;
    const currentMode = modeToggle.getAttribute('data-current-mode') || 'Hybrid Mode';
    
    // Only hide product grid for experts if not in traditional mode
    if (skillLevel === 'expert' && currentMode !== 'Traditional Mode') {
        // Hide product container by default for experts in hybrid/conversation modes
        document.getElementById('productContainer').style.display = 'none';
        gridToggleText.textContent = 'Show Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye-slash mr-1';
    } else {
        // For non-experts or when in traditional mode, show the product container
        document.getElementById('productContainer').style.display = 'grid';
        gridToggleText.textContent = 'Hide Grid';
        toggleGrid.querySelector('i').className = 'fas fa-eye mr-1';
    }
}

function alignOrderWithProducts() {
    try {
        const rightColumn = document.getElementById('orderSummaryColumn');
        const productGrid = document.getElementById('productGrid');

        if (!rightColumn || !productGrid) {
            return;
        }

        // Check if we're on desktop (lg breakpoint and above)
        if (window.innerWidth >= 1024) {
            // Calculate alignment
            // Wait for layout to stabilize before measuring
            requestAnimationFrame(() => {
                try {
                    const productGridRect = productGrid.getBoundingClientRect();
                    const rightColumnRect = rightColumn.getBoundingClientRect();

                    // Calculate the offset needed to align the tops
                    const offset = productGridRect.top - rightColumnRect.top;

                    // Apply the offset as margin-top, but only if it's positive and reasonable
                    // Also check that we're not already aligned (within 5px tolerance)
                    if (offset > 5 && offset < 200) {
                        rightColumn.style.marginTop = `${offset}px`;
                    } else if (Math.abs(offset) <= 5) {
                        // Already aligned, don't change
                        return;
                    } else {
                        rightColumn.style.marginTop = '';
                    }
                } catch (innerError) {
                    console.error('Error in alignment calculation:', innerError);
                }
            });
        } else {
            // On mobile/tablet, always reset to default layout
            rightColumn.style.marginTop = '';
        }
    } catch (error) {
        console.error('Error in alignOrderWithProducts:', error);
        // Fallback: reset to default
        const rightColumn = document.getElementById('orderSummaryColumn');
        if (rightColumn) {
            rightColumn.style.marginTop = '';
        }
    }
}

function savePreferences() {
    localStorage.setItem('simplePosPrefs', JSON.stringify(userPreferences));
    localStorage.setItem('simplePosInsights', JSON.stringify(insightData));
}

// Smart Insights System Functions
function generateInsights() {
    const now = Date.now();
    const insights = [];
    
    // Session-based insights
    const sessionTime = Math.floor((now - insightData.sessionStats.timeSpentInSession) / 60000); // minutes
    
    // Efficiency insights
    if (userPreferences.conversationUsage > userPreferences.buttonUsage * 2) {
        insights.push({
            type: 'efficiency',
            icon: 'fas fa-rocket',
            title: 'Conversation Expert',
            message: 'You\'re mastering natural language! Try combining items like "2 coffees and a muffin"',
            priority: 'medium'
        });
    } else if (userPreferences.buttonUsage > userPreferences.conversationUsage * 2) {
        insights.push({
            type: 'tip',
            icon: 'fas fa-comments',
            title: 'Try Voice Commands',
            message: 'Speed up orders with natural language: "add 3 lattes"',
            priority: 'low'
        });
    }
    
    // Order pattern insights
    if (currentOrder.length > 0) {
        const orderValue = currentOrder.reduce((sum, item) => sum + item.price, 0);
        
        if (orderValue < 10) {
            insights.push({
                type: 'upsell',
                icon: 'fas fa-arrow-up',
                title: 'Upsell Opportunity',
                message: 'Suggest adding a pastry or drink to increase order value',
                priority: 'high'
            });
        }
        
        // Popular combo detection
        const hasFood = currentOrder.some(item => ['Croissant', 'Muffin', 'Sandwich'].includes(item.name));
        const hasDrink = currentOrder.some(item => ['Coffee', 'Latte', 'Fresh Juice'].includes(item.name));
        
        if (hasFood && !hasDrink) {
            insights.push({
                type: 'combo',
                icon: 'fas fa-coffee',
                title: 'Perfect Combo',
                message: 'Customer might want a drink with their food item',
                priority: 'medium'
            });
        } else if (hasDrink && !hasFood) {
            insights.push({
                type: 'combo',
                icon: 'fas fa-cookie-bite',
                title: 'Add a Snack',
                message: 'Suggest a pastry to complement their drink',
                priority: 'medium'
            });
        }
    }
    
    // Performance insights
    if (insightData.sessionStats.ordersCompleted > 0) {
        const avgOrderValue = insightData.sessionStats.totalSales / insightData.sessionStats.ordersCompleted;
        
        if (avgOrderValue > 15) {
            insights.push({
                type: 'achievement',
                icon: 'fas fa-trophy',
                title: 'High-Value Sales',
                message: `Great work! Average order: $${avgOrderValue.toFixed(2)}`,
                priority: 'low'
            });
        }
        
        if (sessionTime > 5 && insightData.sessionStats.ordersCompleted > 3) {
            const ordersPerHour = Math.round((insightData.sessionStats.ordersCompleted / sessionTime) * 60);
            insights.push({
                type: 'performance',
                icon: 'fas fa-chart-line',
                title: 'Productivity Update',
                message: `Processing ~${ordersPerHour} orders/hour. ${ordersPerHour > 10 ? 'Excellent pace!' : 'Keep it up!'}`,
                priority: 'low'
            });
        }
    }
    
    // Usage tips
    if (userPreferences.skillLevel === 'learning' && sessionTime > 2) {
        insights.push({
            type: 'tip',
            icon: 'fas fa-graduation-cap',
            title: 'Learning Progress',
            message: 'Try shortcuts: "checkout", "clear order", or "print receipt"',
            priority: 'medium'
        });
    }
    
    // Time-based insights
    if (sessionTime > 30) {
        insights.push({
            type: 'wellness',
            icon: 'fas fa-clock',
            title: 'Session Time',
            message: `${sessionTime} minutes active. Consider taking a break soon.`,
            priority: 'low'
        });
    }
    
    // Limit insights to top 3 by priority
    const priorityOrder = {'high': 3, 'medium': 2, 'low': 1};
    insights.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
    
    insightData.insights = insights.slice(0, 3);
    insightData.lastInsightUpdate = now;
    
    updateInsightsDisplay();
}

function updateInsightsDisplay() {
    const insightsList = document.getElementById('insightsList');
    if (!insightsList) return;
    
    if (insightData.insights.length === 0) {
        insightsList.innerHTML = `
            <div class="text-center py-3" style="color: var(--text-muted);">
                <i class="fas fa-brain text-lg mb-2" style="color: var(--accent-primary);"></i>
                <p class="text-sm">Analyzing your patterns...</p>
            </div>
        `;
        return;
    }
    
    insightsList.innerHTML = insightData.insights.map(insight => `
        <div class="insight-item p-3 rounded-lg transition-all duration-200" 
             style="background-color: var(--bg-accent); border-left: 3px solid var(--accent-primary);">
            <div class="flex items-start space-x-3">
                <div class="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" 
                     style="background-color: var(--accent-primary);">
                    <i class="${insight.icon} text-xs" style="color: var(--bg-secondary);"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium mb-1" style="color: var(--text-primary);">
                        ${insight.title}
                    </h4>
                    <p class="text-xs leading-relaxed" style="color: var(--text-secondary);">
                        ${insight.message}
                    </p>
                </div>
            </div>
        </div>
    `).join('');
}

function trackOrderCompletion(orderData) {
    const orderValue = orderData.reduce((sum, item) => sum + item.price, 0);
    
    insightData.orderHistory.push({
        items: orderData.slice(),
        total: orderValue,
        timestamp: Date.now(),
        itemCount: orderData.length
    });
    
    insightData.sessionStats.ordersCompleted++;
    insightData.sessionStats.totalSales += orderValue;
    insightData.sessionStats.averageOrderValue = insightData.sessionStats.totalSales / insightData.sessionStats.ordersCompleted;
    
    // Track popular items
    orderData.forEach(item => {
        insightData.sessionStats.mostOrderedItems[item.name] = 
            (insightData.sessionStats.mostOrderedItems[item.name] || 0) + 1;
    });
    
    // Limit order history to last 50 orders
    if (insightData.orderHistory.length > 50) {
        insightData.orderHistory = insightData.orderHistory.slice(-50);
    }
    
    generateInsights();
    savePreferences();
}


// Theme Management Functions - Modern POS 2024
function changeTheme(themeName) {
    const themes = {
        light: { 
            name: 'Light Mode', 
            icon: 'fas fa-sun',
            class: '' 
        },
        dark: { 
            name: 'Dark Mode', 
            icon: 'fas fa-moon',
            class: 'theme-dark' 
        },
        contrast: { 
            name: 'High Contrast', 
            icon: 'fas fa-eye',
            class: 'theme-contrast' 
        },
        cafe: { 
            name: 'Café', 
            icon: 'fas fa-coffee',
            class: 'theme-cafe' 
        },
        corporate: { 
            name: 'Corporate', 
            icon: 'fas fa-building',
            class: 'theme-corporate' 
        },
        restaurant: { 
            name: 'Restaurant', 
            icon: 'fas fa-utensils',
            class: 'theme-restaurant' 
        },
        health: { 
            name: 'Health', 
            icon: 'fas fa-leaf',
            class: 'theme-health' 
        }
    };

    const theme = themes[themeName];
    if (!theme) return;

    // Remove all theme classes
    document.body.classList.remove('theme-dark', 'theme-contrast', 'theme-cafe', 'theme-corporate', 'theme-restaurant', 'theme-health');
    
    // Add new theme class (if not default light)
    if (theme.class) {
        document.body.classList.add(theme.class);
    }

    // Update theme state
    currentTheme = themeName;
    userPreferences.theme = themeName;

    // Update UI elements
    currentThemeSpan.textContent = theme.name;
    themeToggle.querySelector('i').className = `${theme.icon} mr-1 sm:mr-2`;

    // Save preference
    savePreferences();
}

function loadTheme() {
    const savedPrefs = localStorage.getItem('simplePosPrefs');
    if (savedPrefs) {
        const prefs = JSON.parse(savedPrefs);
        if (prefs.theme) {
            changeTheme(prefs.theme);
        }
    }
}

// Categories System
let currentCategory = 'popular';

function initializeCategoriesSystem() {
    console.log('Initializing categories system...');
    
    // Initialize responsive category navigation
    initializeCategoryScroll();
    initializeCategoryKeyboard();
    
    // Set up category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    console.log('Found category tabs:', categoryTabs.length);
    
    categoryTabs.forEach((tab, index) => {
        console.log(`Setting up tab ${index}:`, tab.dataset.category);
        tab.addEventListener('click', (e) => {
            console.log('Category tab clicked:', tab.dataset.category);
            e.preventDefault();
            selectCategory(tab);
        });
    });
    
    // Initialize with popular category after a short delay to ensure DOM is ready
    setTimeout(() => {
        const defaultTab = document.querySelector('.category-tab[data-category="popular"]');
        console.log('Default tab found:', defaultTab);
        if (defaultTab) {
            selectCategory(defaultTab);
        }
    }, 100);
}

function selectCategory(tabElement) {
    const category = tabElement.dataset.category;
    console.log('selectCategory called with:', category);
    filterByCategory(category);
    
    // Update active tab state and ARIA attributes
    document.querySelectorAll('.category-tab').forEach(t => {
        t.classList.remove('active');
        t.setAttribute('aria-selected', 'false');
        t.setAttribute('tabindex', '-1');
    });
    
    tabElement.classList.add('active');
    tabElement.setAttribute('aria-selected', 'true');
    tabElement.setAttribute('tabindex', '0');
    
    // Close any open menus
    closeAllMenus();
}

function closeAllMenus() {
    // Function kept for compatibility with other menu systems
    // Currently no menus to close in category system after removing overflow functionality
}



function initializeCategoryScroll() {
    const leftBtn = document.getElementById('scrollLeftBtn');
    const rightBtn = document.getElementById('scrollRightBtn');
    const container = document.getElementById('categoryTabsContainer');
    
    if (!leftBtn || !rightBtn || !container) return;
    
    const scrollAmount = 150; // px
    
    leftBtn.addEventListener('click', () => {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
        setTimeout(updateScrollButtons, 150);
    });
    
    rightBtn.addEventListener('click', () => {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
        setTimeout(updateScrollButtons, 150);
    });
    
    // Update scroll button visibility
    function updateScrollButtons() {
        const { scrollLeft, scrollWidth, clientWidth } = container;
        
        leftBtn.style.display = scrollLeft > 0 ? 'flex' : 'none';
        rightBtn.style.display = scrollLeft < scrollWidth - clientWidth - 1 ? 'flex' : 'none';
    }
    
    // Update on scroll and resize
    container.addEventListener('scroll', updateScrollButtons);
    window.addEventListener('resize', updateScrollButtons);
    
    // Initial update
    setTimeout(updateScrollButtons, 100);
}





function initializeCategoryKeyboard() {
    const container = document.getElementById('categoryTabsContainer');
    if (!container) return;
    
    // Arrow key navigation
    container.addEventListener('keydown', (e) => {
        const tabs = Array.from(container.querySelectorAll('.category-tab:not([style*="display: none"])'));
        const currentIndex = tabs.indexOf(document.activeElement);
        
        let newIndex = currentIndex;
        
        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                newIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                break;
            case 'ArrowRight':
                e.preventDefault();
                newIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                break;
            case 'Home':
                e.preventDefault();
                newIndex = 0;
                break;
            case 'End':
                e.preventDefault();
                newIndex = tabs.length - 1;
                break;
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (currentIndex >= 0) {
                    selectCategory(tabs[currentIndex]);
                }
                return;
        }
        
        if (newIndex !== currentIndex && tabs[newIndex]) {
            tabs[newIndex].focus();
        }
    });
}




function filterByCategory(category) {
    console.log('filterByCategory called with:', category);
    currentCategory = category;
    const productCards = document.querySelectorAll('.product-card');
    console.log('Found product cards:', productCards.length);
    
    let shownCount = 0;
    productCards.forEach(card => {
        const cardCategory = card.dataset.category;
        const cardPopular = card.dataset.popular === 'true';
        
        let shouldShow = false;
        
        if (category === 'all') {
            shouldShow = true;
        } else if (category === 'popular') {
            shouldShow = cardPopular;
        } else {
            shouldShow = cardCategory === category;
        }
        
        if (shouldShow) {
            card.style.display = 'block';
            card.style.opacity = '1';
            shownCount++;
        } else {
            card.style.display = 'none';
            card.style.opacity = '0';
        }
    });
    console.log(`Filtered to category '${category}': showing ${shownCount} out of ${productCards.length} products`);
}

// Setup mobile tab switching functionality
function setupMobileTabs() {
    const productsTab = document.getElementById('productsTab');
    const orderTab = document.getElementById('orderTab');
    const productsTabContent = document.getElementById('productsTabContent');
    const orderTabContent = document.getElementById('orderTabContent');
    
    // Check if we're on mobile (function will only work on mobile anyway due to CSS)
    function switchTab(activeTab, activeContent, inactiveTab, inactiveContent) {
        // Remove active states
        inactiveTab.classList.remove('active-tab');
        inactiveContent.classList.remove('active-content');
        inactiveContent.classList.add('hidden');
        
        // Add active states
        activeTab.classList.add('active-tab');
        activeContent.classList.add('active-content');
        activeContent.classList.remove('hidden');
        
        // Add body classes for CSS targeting
        document.body.classList.remove('mobile-products-active', 'mobile-order-active');
        if (activeTab.id === 'productsTab') {
            document.body.classList.add('mobile-products-active');
        } else if (activeTab.id === 'orderTab') {
            document.body.classList.add('mobile-order-active');
        }
    }
    
    // Products tab click handler
    if (productsTab && productsTabContent) {
        productsTab.addEventListener('click', function() {
            switchTab(productsTab, productsTabContent, orderTab, orderTabContent);
            // Move content to mobile containers
            moveContentToMobileContainers('products');
        });
    }
    
    // Order tab click handler  
    if (orderTab && orderTabContent) {
        orderTab.addEventListener('click', function() {
            switchTab(orderTab, orderTabContent, productsTab, productsTabContent);
            // Move content to mobile containers
            moveContentToMobileContainers('order');
        });
    }
    
    // Initialize mobile tab state
    if (window.innerWidth <= 1024) {
        document.body.classList.add('mobile-products-active');
        // Move initial content
        moveContentToMobileContainers('products');
    }
    
    // Handle window resize
    window.addEventListener('resize', handleResponsiveLayout);
    
    // Initial layout setup
    handleResponsiveLayout();
}

// Handle responsive layout changes
function handleResponsiveLayout() {
    const isMobile = window.innerWidth <= 1024;
    const mobileTabNav = document.getElementById('mobileTabNav');
    const desktopLayout = document.getElementById('desktopLayout');
    const mobileTabContent = document.getElementById('mobileTabContent');
    
    if (isMobile) {
        // Show mobile layout
        if (mobileTabNav) mobileTabNav.classList.remove('hidden');
        if (desktopLayout) desktopLayout.classList.add('hidden');
        if (mobileTabContent) mobileTabContent.classList.remove('hidden');
        
        // Set appropriate body class for active tab
        const activeTab = document.querySelector('.mobile-tab.active-tab');
        document.body.classList.remove('mobile-products-active', 'mobile-order-active');
        if (activeTab && activeTab.id === 'orderTab') {
            document.body.classList.add('mobile-order-active');
            moveContentToMobileContainers('order');
        } else {
            document.body.classList.add('mobile-products-active');
            moveContentToMobileContainers('products');
        }
    } else {
        // Show desktop layout
        if (mobileTabNav) mobileTabNav.classList.add('hidden');
        if (desktopLayout) desktopLayout.classList.remove('hidden');
        if (mobileTabContent) mobileTabContent.classList.add('hidden');
        
        // Remove mobile body classes
        document.body.classList.remove('mobile-products-active', 'mobile-order-active');
        
        // Restore content to desktop layout
        restoreDesktopLayout();
    }
}

// Move content between desktop and mobile containers based on screen size
function moveContentToMobileContainers(activeTab = 'products') {
    if (window.innerWidth > 1024) return; // Only move on mobile
    
    // Get the content elements
    const productGrid = document.getElementById('productGrid');
    const smartInsights = document.getElementById('smartInsights');
    const conversationPanel = document.getElementById('conversationArea');
    const orderContainer = document.querySelector('.order-container');
    
    // Get mobile tab containers
    const productsTabContent = document.getElementById('productsTabContent');
    const orderTabContent = document.getElementById('orderTabContent');
    
    if (activeTab === 'products') {
        // Move products-related content to Products tab
        if (productGrid && productsTabContent && !productsTabContent.contains(productGrid)) {
            productsTabContent.appendChild(productGrid);
            // Re-initialize category system after moving product grid
            setTimeout(() => {
                console.log('Re-initializing categories after mobile content move');
                initializeCategoriesSystem();
            }, 50);
        }
        if (smartInsights && productsTabContent && !productsTabContent.contains(smartInsights)) {
            productsTabContent.appendChild(smartInsights);
        }
        if (conversationPanel && productsTabContent && !productsTabContent.contains(conversationPanel)) {
            productsTabContent.appendChild(conversationPanel);
        }
    } else if (activeTab === 'order') {
        // Move order-related content to Order tab
        if (orderContainer && orderTabContent && !orderTabContent.contains(orderContainer)) {
            orderTabContent.appendChild(orderContainer);
        }
    }
}

// Helper function to find the conversation panel
function findConversationPanel() {
    return document.getElementById('conversationArea');
}

// Restore content to desktop layout positions
function restoreDesktopLayout() {
    // Get the original desktop containers
    const leftColumn = document.querySelector('#desktopLayout .lg\\:col-span-2');
    const rightColumn = document.querySelector('#desktopLayout .lg\\:col-span-1');
    
    // Get content elements wherever they are now
    const productGrid = document.getElementById('productGrid');
    const smartInsights = document.getElementById('smartInsights');
    const conversationPanel = document.getElementById('conversationArea');
    const orderContainer = document.querySelector('.order-container');
    
    if (leftColumn && rightColumn) {
        // Move products and conversation back to left column
        if (productGrid && !leftColumn.contains(productGrid)) {
            leftColumn.appendChild(productGrid);
        }
        if (conversationPanel && !leftColumn.contains(conversationPanel)) {
            leftColumn.appendChild(conversationPanel);
        }
        
        // Move insights and order back to right column
        if (smartInsights && !rightColumn.contains(smartInsights)) {
            rightColumn.insertBefore(smartInsights, rightColumn.firstChild);
        }
        if (orderContainer && !rightColumn.contains(orderContainer)) {
            rightColumn.appendChild(orderContainer);
        }
    }
}

// Update tab badges with current order info
function updateTabBadges() {
    const productsTabBadge = document.getElementById('productsTabBadge');
    const orderTabBadge = document.getElementById('orderTabBadge');
    
    if (productsTabBadge) {
        const itemCount = currentOrder.length;
        if (itemCount > 0) {
            productsTabBadge.textContent = itemCount;
            productsTabBadge.classList.remove('hidden');
        } else {
            productsTabBadge.classList.add('hidden');
        }
    }
    
    if (orderTabBadge) {
        const orderTotal = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        if (orderTotal > 0) {
            orderTabBadge.textContent = `$${orderTotal.toFixed(2)}`;
            orderTabBadge.classList.remove('hidden');
        } else {
            orderTabBadge.classList.add('hidden');
        }
    }
}


// Note: initializeCategoriesSystem() is now called in the main DOMContentLoaded handler above

// Make functions globally accessible
window.removeItem = removeItem;
window.removeItemGroup = removeItemGroup;
window.adjustQuantity = adjustQuantity;

// Auto-save preferences periodically
setInterval(savePreferences, 30000);
